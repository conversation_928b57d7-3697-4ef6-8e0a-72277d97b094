@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800;900&family=Source+Sans+Pro:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');

@import "tailwindcss";

/* Tailwind CSS v4 Configuration */
@theme {
  --font-family-sans: 'Source Sans Pro', 'Inter', system-ui, sans-serif;
  --font-family-heading: 'Montserrat', 'Inter', system-ui, sans-serif;

  /* Construction Industry Color Palette */
  --color-slate-50: #f8fafc;
  --color-slate-100: #f1f5f9;
  --color-slate-200: #e2e8f0;
  --color-slate-300: #cbd5e1;
  --color-slate-400: #94a3b8;
  --color-slate-500: #64748b;
  --color-slate-600: #475569;
  --color-slate-700: #334155;
  --color-slate-800: #1e293b;
  --color-slate-900: #0f172a;

  /* Blue Color Palette - Matching Arebone Logo */
  --color-blue-50: #eff6ff;
  --color-blue-100: #dbeafe;
  --color-blue-200: #bfdbfe;
  --color-blue-300: #93c5fd;
  --color-blue-400: #60a5fa;
  --color-blue-500: #3b82f6;
  --color-blue-600: #2563eb;
  --color-blue-700: #1d4ed8;
  --color-blue-800: #1e40af;
  --color-blue-900: #1e3a8a;

  /* Primary Brand Blue - Matching Logo */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  /* Orange Color Palette - Construction Industry Accent */
  --color-orange-50: #fff7ed;
  --color-orange-100: #ffedd5;
  --color-orange-200: #fed7aa;
  --color-orange-300: #fdba74;
  --color-orange-400: #fb923c;
  --color-orange-500: #f97316;
  --color-orange-600: #ea580c;
  --color-orange-700: #c2410c;
  --color-orange-800: #9a3412;
  --color-orange-900: #7c2d12;

  /* Construction Brand Orange - Accent Color */
  --color-accent-50: #fff7ed;
  --color-accent-100: #ffedd5;
  --color-accent-200: #fed7aa;
  --color-accent-300: #fdba74;
  --color-accent-400: #fb923c;
  --color-accent-500: #f97316;
  --color-accent-600: #ea580c;
  --color-accent-700: #c2410c;
  --color-accent-800: #9a3412;
  --color-accent-900: #7c2d12;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Source Sans Pro', 'Inter', system-ui, sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
  margin: 0;
  padding: 0;
}

.btn-primary {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.2);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-1px);
  box-shadow: 0 8px 15px -3px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
  background-color: white;
  color: #2563eb;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  border: 2px solid #2563eb;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px -1px rgba(37, 99, 235, 0.1);
}

.btn-secondary:hover {
  background-color: #2563eb;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 8px 15px -3px rgba(37, 99, 235, 0.2);
}

.section-padding {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

@media (min-width: 1024px) {
  .section-padding {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }
}

.container-custom {
  max-width: 80rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-custom {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-custom {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.heading-xl {
  font-family: 'Montserrat', 'Inter', system-ui, sans-serif;
  font-size: 2.25rem;
  font-weight: 800;
  line-height: 1.15;
  letter-spacing: -0.03em;
}

@media (min-width: 1024px) {
  .heading-xl {
    font-size: 3rem;
    line-height: 1.1;
  }
}

@media (min-width: 1280px) {
  .heading-xl {
    font-size: 3.75rem;
    line-height: 1.05;
  }
}

.heading-lg {
  font-family: 'Montserrat', 'Inter', system-ui, sans-serif;
  font-size: 1.875rem;
  font-weight: 700;
  line-height: 1.3;
  letter-spacing: -0.02em;
}

@media (min-width: 1024px) {
  .heading-lg {
    font-size: 2.25rem;
    line-height: 1.25;
  }
}

.heading-md {
  font-family: 'Montserrat', 'Inter', system-ui, sans-serif;
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.3;
  letter-spacing: -0.015em;
}

@media (min-width: 1024px) {
  .heading-md {
    font-size: 1.875rem;
  }
}

.text-body {
  color: #4b5563;
  line-height: 1.625;
}

.font-heading {
  font-family: 'Montserrat', 'Inter', system-ui, sans-serif;
}

/* Enhanced Typography Classes */
.hero-headline {
  font-family: 'Montserrat', 'Inter', system-ui, sans-serif;
  font-weight: 900;
  line-height: 1.1;
  letter-spacing: -0.04em;
}

.hero-subheading {
  font-family: 'Montserrat', 'Inter', system-ui, sans-serif;
  font-weight: 600;
  line-height: 1.3;
  letter-spacing: -0.015em;
}

.hero-body-text {
  font-family: 'Source Sans Pro', 'Inter', system-ui, sans-serif;
  line-height: 1.6;
  letter-spacing: 0.01em;
}

.hero-feature-text {
  font-family: 'Source Sans Pro', 'Inter', system-ui, sans-serif;
  line-height: 1.5;
  letter-spacing: 0.005em;
}

.hero-stat-number {
  font-family: 'Montserrat', 'Inter', system-ui, sans-serif;
  font-weight: 900;
  line-height: 1;
  letter-spacing: -0.02em;
}

.hero-stat-label {
  font-family: 'Source Sans Pro', 'Inter', system-ui, sans-serif;
  font-weight: 500;
  line-height: 1.3;
  letter-spacing: 0.02em;
}

/* Systematic Spacing System (8px increments) */
.space-xs { margin: 0.5rem; }      /* 8px */
.space-sm { margin: 1rem; }        /* 16px */
.space-md { margin: 1.5rem; }      /* 24px */
.space-lg { margin: 2rem; }        /* 32px */
.space-xl { margin: 2.5rem; }      /* 40px */
.space-2xl { margin: 3rem; }       /* 48px */

.space-y-xs > * + * { margin-top: 0.5rem; }   /* 8px */
.space-y-sm > * + * { margin-top: 1rem; }     /* 16px */
.space-y-md > * + * { margin-top: 1.5rem; }   /* 24px */
.space-y-lg > * + * { margin-top: 2rem; }     /* 32px */
.space-y-xl > * + * { margin-top: 2.5rem; }   /* 40px */
.space-y-2xl > * + * { margin-top: 3rem; }    /* 48px */

.gap-xs { gap: 0.5rem; }          /* 8px */
.gap-sm { gap: 1rem; }            /* 16px */
.gap-md { gap: 1.5rem; }          /* 24px */
.gap-lg { gap: 2rem; }            /* 32px */
.gap-xl { gap: 2.5rem; }          /* 40px */
.gap-2xl { gap: 3rem; }           /* 48px */

/* Hero Section Specific Spacing */
.hero-section-spacing {
  padding-top: 4rem;    /* 64px */
  padding-bottom: 4rem; /* 64px */
}

@media (min-width: 1024px) {
  .hero-section-spacing {
    padding-top: 6rem;    /* 96px */
    padding-bottom: 6rem; /* 96px */
  }
}

/* Enhanced Visual Design Elements */
.hero-gradient-bg {
  background: linear-gradient(135deg, #ffffff 0%, #eff6ff 50%, #dbeafe 100%);
  position: relative;
}

.hero-gradient-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(30, 64, 175, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

/* Enhanced Feature Icons */
.feature-icon-container {
  background: linear-gradient(135deg, #bfdbfe, #93c5fd);
  border: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.feature-icon-container:hover {
  background: linear-gradient(135deg, #93c5fd, #60a5fa);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
  transform: translateY(-1px);
}

/* Enhanced Stats */
.stat-container {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-container:hover {
  transform: translateY(-2px);
}

.stat-number {
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(59, 130, 246, 0.1));
}

/* Enhanced CTA Section */
.cta-benefits {
  background: linear-gradient(90deg, #eff6ff, #dbeafe);
  border: 1px solid #bfdbfe;
  border-radius: 0.75rem;
  padding: 1rem 1.5rem;
  backdrop-filter: blur(10px);
}

/* Accessibility and Contrast Improvements */
.text-blue-contrast {
  color: #1e40af; /* Darker blue for better contrast */
}

.text-blue-accessible {
  color: #1d4ed8; /* WCAG AA compliant blue */
}

.bg-blue-accessible {
  background-color: #2563eb; /* Accessible blue background */
}

.border-blue-accessible {
  border-color: #3b82f6; /* Accessible blue border */
}

/* Subtle geometric elements */
.geometric-accent {
  position: absolute;
  background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05));
  border-radius: 50%;
  filter: blur(1px);
}

/* Enhanced shadows for depth */
.enhanced-shadow {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(0, 0, 0, 0.05);
}

.enhanced-shadow-lg {
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(0, 0, 0, 0.05);
}

/* Construction-themed visual elements */
.construction-pattern {
  background-image:
    linear-gradient(45deg, transparent 35%, rgba(255, 255, 255, 0.1) 35%, rgba(255, 255, 255, 0.1) 65%, transparent 65%),
    linear-gradient(-45deg, transparent 35%, rgba(255, 255, 255, 0.1) 35%, rgba(255, 255, 255, 0.1) 65%, transparent 65%);
  background-size: 20px 20px;
}

.header-shadow {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(0, 0, 0, 0.05);
}

/* Professional hover effects */
.professional-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.professional-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Trust indicator styles */
.trust-indicator {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 1px solid #cbd5e1;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: #475569;
}

/* Mobile menu animation */
.mobile-menu-enter {
  opacity: 0;
  transform: translateY(-10px);
}

.mobile-menu-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Image optimization */
img {
  max-width: 100%;
  height: auto;
}

picture img {
  display: block;
  width: 100%;
}

/* Enhanced Logo styling */
.logo-container {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.logo-container:hover {
  transform: translateY(-1px);
}

.logo-container img {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.logo-container:hover img {
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
}

/* Responsive logo sizing - Removed to allow Tailwind classes to work */

/* Full-screen hero background image optimization */
.hero-background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* Enhanced text shadows for better readability on background images */
.hero-text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5), 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Modern construction-themed button styles */
.btn-primary-orange {
  background: linear-gradient(135deg, #ea580c, #c2410c);
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  box-shadow: 0 4px 6px -1px rgba(234, 88, 12, 0.2);
}

.btn-primary-orange:hover {
  background: linear-gradient(135deg, #c2410c, #9a3412);
  transform: translateY(-1px);
  box-shadow: 0 8px 15px -3px rgba(234, 88, 12, 0.3);
}

/* Enhanced orange accent styles */
.text-orange-contrast {
  color: #9a3412; /* Darker orange for better contrast */
}

.text-orange-accessible {
  color: #c2410c; /* WCAG AA compliant orange */
}

.bg-orange-accessible {
  background-color: #ea580c; /* Accessible orange background */
}

.border-orange-accessible {
  border-color: #f97316; /* Accessible orange border */
}

/* Responsive background image media queries */
@media (max-width: 767px) {
  .hero-background-image {
    object-position: center center;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .hero-background-image {
    object-position: center center;
  }
}

@media (min-width: 1024px) {
  .hero-background-image {
    object-position: center center;
  }
}

/* Modern Hero Section Animations */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes float-slow {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) translateX(10px);
    opacity: 0.6;
  }
}

@keyframes float-medium {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.2;
  }
  50% {
    transform: translateY(-15px) translateX(-8px);
    opacity: 0.5;
  }
}

@keyframes float-fast {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.4;
  }
  50% {
    transform: translateY(-25px) translateX(15px);
    opacity: 0.7;
  }
}

/* Animation Classes */
.animate-fade-in-up {
  animation: fade-in-up 1s ease-out forwards;
  opacity: 0;
}

.animate-slide-in-left {
  animation: slide-in-left 1s ease-out forwards;
  opacity: 0;
}

.animate-slide-in-right {
  animation: slide-in-right 1s ease-out forwards;
  opacity: 0;
}

.animate-fade-in {
  animation: fade-in 1s ease-out forwards;
  opacity: 0;
}

.animate-float-slow {
  animation: float-slow 8s ease-in-out infinite;
}

.animate-float-medium {
  animation: float-medium 6s ease-in-out infinite;
}

.animate-float-fast {
  animation: float-fast 4s ease-in-out infinite;
}

/* Enhanced top banner animations */
.top-banner-item {
  transition: all 0.3s ease;
}

.top-banner-item:hover {
  transform: translateY(-1px);
}

/* Certification badges */
.cert-badge {
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.cert-badge:hover {
  transform: scale(1.05);
  background-color: rgba(249, 115, 22, 0.2);
}

/* Auto-scroll carousel animations */
@keyframes scroll-logos {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* Logo carousel container styles */
.logo-carousel-container {
  overflow: hidden;
  position: relative;
  width: 100%;
  mask: linear-gradient(
    to right,
    transparent 0%,
    black 10%,
    black 90%,
    transparent 100%
  );
  -webkit-mask: linear-gradient(
    to right,
    transparent 0%,
    black 10%,
    black 90%,
    transparent 100%
  );
}

.logo-carousel-track {
  display: flex;
  animation: scroll-logos 30s linear infinite;
  width: calc(200%);
}

.logo-carousel-item {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.logo-carousel-item:hover {
  transform: scale(1.05);
}

.logo-carousel-item img {
  width: 120px;
  height: 80px;
  object-fit: contain;
  filter: grayscale(1) opacity(0.7);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.logo-carousel-item:hover img {
  filter: grayscale(0) opacity(1);
  transform: scale(1.1);
}

/* Responsive logo sizing */
@media (max-width: 640px) {
  .logo-carousel-item {
    padding: 0.75rem 1.5rem;
  }

  .logo-carousel-item img {
    width: 100px;
    height: 60px;
  }

  .logo-carousel-track {
    animation-duration: 25s;
  }
}

@media (min-width: 1024px) {
  .logo-carousel-item {
    padding: 1.25rem 2.5rem;
  }

  .logo-carousel-item img {
    width: 140px;
    height: 90px;
  }

  .logo-carousel-track {
    animation-duration: 35s;
  }
}

/* Pause animation on hover for accessibility */
.logo-carousel-container:hover .logo-carousel-track {
  animation-play-state: paused;
}

/* Modern Header Styling */
.modern-header {
  background: linear-gradient(135deg,
    rgba(15, 23, 42, 0.98) 0%,
    rgba(30, 41, 59, 0.95) 50%,
    rgba(51, 65, 85, 0.92) 100%
  );
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.2),
    0 2px 4px -1px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.modern-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at 20% 50%,
    rgba(59, 130, 246, 0.03) 0%,
    transparent 50%
  ),
  radial-gradient(
    circle at 80% 50%,
    rgba(249, 115, 22, 0.02) 0%,
    transparent 50%
  );
  pointer-events: none;
  z-index: -1;
}

.modern-nav-item {
  position: relative;
  padding: 0.75rem 1.25rem;
  border-radius: 0.75rem;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #e2e8f0;
  background: transparent;
}

.modern-nav-item:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.2),
    0 2px 4px -1px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.modern-nav-item.active {
  color: #f97316;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(249, 115, 22, 0.1) 100%
  );
  backdrop-filter: blur(15px);
  box-shadow:
    0 4px 6px -1px rgba(249, 115, 22, 0.3),
    0 2px 4px -1px rgba(249, 115, 22, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(249, 115, 22, 0.2);
}

.modern-nav-item.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background: linear-gradient(135deg, #f97316, #ea580c);
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(249, 115, 22, 0.4);
}

.modern-cta-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  font-weight: 600;
  padding: 0.875rem 1.75rem;
  border-radius: 0.75rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 6px -1px rgba(59, 130, 246, 0.3),
    0 2px 4px -1px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.modern-cta-primary:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-2px);
  box-shadow:
    0 8px 15px -3px rgba(59, 130, 246, 0.4),
    0 4px 6px -2px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.modern-cta-secondary {
  background: linear-gradient(135deg,
    rgba(51, 65, 85, 0.95) 0%,
    rgba(30, 41, 59, 0.95) 100%
  );
  color: white;
  font-weight: 600;
  padding: 0.875rem 1.75rem;
  border-radius: 0.75rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.3),
    0 2px 4px -1px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.modern-cta-secondary:hover {
  background: linear-gradient(135deg,
    rgba(30, 41, 59, 0.95) 0%,
    rgba(15, 23, 42, 0.95) 100%
  );
  transform: translateY(-2px);
  box-shadow:
    0 8px 15px -3px rgba(0, 0, 0, 0.4),
    0 4px 6px -2px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.modern-mobile-menu {
  background: linear-gradient(135deg,
    rgba(15, 23, 42, 0.95) 0%,
    rgba(30, 41, 59, 0.9) 100%
  );
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 1rem;
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.3),
    0 4px 6px -2px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  margin-top: 1rem;
}

.modern-mobile-nav-item {
  padding: 1rem 1.25rem;
  border-radius: 0.75rem;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #e2e8f0;
  margin: 0.25rem;
}

.modern-mobile-nav-item:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  box-shadow:
    0 2px 4px -1px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.modern-mobile-nav-item.active {
  color: #f97316;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(249, 115, 22, 0.1) 100%
  );
  border-left: 4px solid #f97316;
  box-shadow:
    0 2px 4px -1px rgba(249, 115, 22, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.modern-top-bar {
  background: linear-gradient(135deg,
    rgba(15, 23, 42, 0.98) 0%,
    rgba(30, 41, 59, 0.95) 50%,
    rgba(51, 65, 85, 0.92) 100%
  );
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
}

.modern-top-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(59, 130, 246, 0.05) 50%,
    transparent 100%
  );
  pointer-events: none;
}

.modern-mobile-button {
  padding: 0.75rem;
  border-radius: 0.75rem;
  color: #e2e8f0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-mobile-button:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.2),
    0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

/* Responsive Header Enhancements */
@media (max-width: 640px) {
  .modern-header {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
  }

  .modern-mobile-menu {
    margin: 0.5rem;
    border-radius: 0.75rem;
  }

  .modern-cta-primary,
  .modern-cta-secondary {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
  }
}

@media (min-width: 641px) and (max-width: 1023px) {
  .modern-nav-item {
    padding: 0.625rem 1rem;
    font-size: 0.875rem;
  }

  .modern-cta-primary,
  .modern-cta-secondary {
    padding: 0.75rem 1.25rem;
    font-size: 0.875rem;
  }
}

@media (min-width: 1024px) {
  .modern-header {
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
  }

  .modern-nav-item {
    margin: 0 0.125rem;
  }

  .modern-cta-primary,
  .modern-cta-secondary {
    font-size: 0.875rem;
  }
}

@media (min-width: 1280px) {
  .modern-nav-item {
    padding: 0.875rem 1.5rem;
    margin: 0 0.25rem;
  }

  .modern-cta-primary,
  .modern-cta-secondary {
    padding: 1rem 2rem;
    font-size: 0.9rem;
  }
}

/* Enhanced focus states for accessibility */
.modern-nav-item:focus,
.modern-mobile-nav-item:focus,
.modern-cta-primary:focus,
.modern-cta-secondary:focus,
.modern-mobile-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .modern-nav-item,
  .modern-mobile-nav-item,
  .modern-cta-primary,
  .modern-cta-secondary,
  .modern-mobile-button {
    transition: none;
  }

  .modern-nav-item:hover,
  .modern-cta-primary:hover,
  .modern-cta-secondary:hover {
    transform: none;
  }
}

/* Modern About Section Styling */
.modern-about-section {
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.95) 0%,
    rgba(241, 245, 249, 0.9) 50%,
    rgba(226, 232, 240, 0.85) 100%
  );
  position: relative;
  overflow: hidden;
}

.modern-about-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at 20% 30%,
    rgba(59, 130, 246, 0.03) 0%,
    transparent 50%
  ),
  radial-gradient(
    circle at 80% 70%,
    rgba(249, 115, 22, 0.02) 0%,
    transparent 50%
  );
  pointer-events: none;
}

.modern-content-container {
  position: relative;
  z-index: 1;
}

.modern-heading-primary {
  font-weight: 800;
  letter-spacing: -0.025em;
  line-height: 1.1;
  background: linear-gradient(135deg, #0f172a 0%, #334155 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.modern-text-enhanced {
  font-weight: 500;
  line-height: 1.7;
  color: #475569;
  letter-spacing: 0.01em;
}

.modern-mission-card {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 100%
  );
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(249, 115, 22, 0.2);
  border-left: 4px solid;
  border-left-color: #ea580c;
  border-radius: 1rem;
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.modern-mission-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(249, 115, 22, 0.02) 0%,
    transparent 50%
  );
  pointer-events: none;
}

.modern-cta-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (min-width: 640px) {
  .modern-cta-container {
    flex-direction: row;
  }
}

.modern-btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  font-weight: 600;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 6px -1px rgba(59, 130, 246, 0.3),
    0 2px 4px -1px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  letter-spacing: 0.025em;
}

.modern-btn-primary:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-2px);
  box-shadow:
    0 8px 15px -3px rgba(59, 130, 246, 0.4),
    0 4px 6px -2px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.modern-btn-secondary {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 100%
  );
  color: #2563eb;
  font-weight: 600;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  border: 2px solid rgba(37, 99, 235, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  letter-spacing: 0.025em;
}

.modern-btn-secondary:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: white;
  border-color: #2563eb;
  transform: translateY(-2px);
  box-shadow:
    0 8px 15px -3px rgba(37, 99, 235, 0.4),
    0 4px 6px -2px rgba(37, 99, 235, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Modern Core Values Section */
.modern-values-container {
  position: relative;
}

.modern-values-heading {
  font-weight: 700;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #0f172a 0%, #334155 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modern-values-description {
  font-weight: 500;
  color: #64748b;
  letter-spacing: 0.01em;
  line-height: 1.6;
}

.modern-values-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 640px) {
  .modern-values-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.modern-value-card {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 100%
  );
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  padding: 1.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.modern-value-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at 50% 0%,
    rgba(249, 115, 22, 0.02) 0%,
    transparent 50%
  );
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modern-value-card:hover {
  transform: translateY(-4px);
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.15),
    0 4px 6px -2px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: rgba(249, 115, 22, 0.1);
}

.modern-value-card:hover::before {
  opacity: 1;
}

.modern-value-icon {
  background: linear-gradient(135deg,
    rgba(249, 115, 22, 0.1) 0%,
    rgba(234, 88, 12, 0.05) 100%
  );
  border: 1px solid rgba(249, 115, 22, 0.2);
  border-radius: 0.75rem;
  padding: 0.75rem;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.modern-value-card:hover .modern-value-icon {
  background: linear-gradient(135deg,
    rgba(249, 115, 22, 0.15) 0%,
    rgba(234, 88, 12, 0.1) 100%
  );
  border-color: rgba(249, 115, 22, 0.3);
  transform: scale(1.05);
}

.modern-value-title {
  font-weight: 600;
  color: #0f172a;
  margin-bottom: 0.5rem;
  letter-spacing: -0.01em;
}

.modern-value-description {
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.6;
  font-weight: 500;
}

.modern-achievement-card {
  background: linear-gradient(135deg,
    rgba(249, 115, 22, 0.95) 0%,
    rgba(234, 88, 12, 0.9) 100%
  );
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  padding: 1.5rem;
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 10px 15px -3px rgba(249, 115, 22, 0.4),
    0 4px 6px -2px rgba(249, 115, 22, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.modern-achievement-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%
  );
  pointer-events: none;
}

.modern-achievement-icon {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 0.75rem;
  padding: 0.75rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.modern-achievement-title {
  font-weight: 600;
  font-size: 1.125rem;
  letter-spacing: -0.01em;
}

.modern-achievement-description {
  color: rgba(249, 115, 22, 0.1);
  font-size: 0.875rem;
  line-height: 1.5;
  font-weight: 500;
}

/* Modern CTA Section Styling */
.modern-cta-section {
  position: relative;
  margin-top: 4rem;
  text-align: center;
}

.modern-cta-container {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.95) 0%,
    rgba(37, 99, 235, 0.9) 50%,
    rgba(29, 78, 216, 0.85) 100%
  );
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1.5rem;
  padding: 3rem 2rem;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 20px 25px -5px rgba(59, 130, 246, 0.3),
    0 10px 10px -5px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.modern-cta-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at 30% 20%,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%
  ),
  radial-gradient(
    circle at 70% 80%,
    rgba(249, 115, 22, 0.1) 0%,
    transparent 50%
  );
  pointer-events: none;
}

.modern-cta-container::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.2) 0%,
    transparent 50%,
    rgba(249, 115, 22, 0.1) 100%
  );
  border-radius: 1.5rem;
  z-index: -1;
  opacity: 0.5;
}

.modern-cta-content {
  position: relative;
  z-index: 1;
}

.modern-cta-heading {
  font-size: 2.5rem;
  font-weight: 800;
  letter-spacing: -0.025em;
  line-height: 1.1;
  color: white;
  margin-bottom: 1.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

@media (min-width: 768px) {
  .modern-cta-heading {
    font-size: 3rem;
  }
}

@media (min-width: 1024px) {
  .modern-cta-heading {
    font-size: 3.5rem;
  }
}

.modern-cta-description {
  font-size: 1.125rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.7;
  margin-bottom: 2rem;
  max-width: 42rem;
  margin-left: auto;
  margin-right: auto;
  letter-spacing: 0.01em;
}

.modern-cta-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  justify-content: center;
  align-items: center;
}

@media (min-width: 640px) {
  .modern-cta-buttons {
    flex-direction: row;
    gap: 1.5rem;
  }
}

.modern-cta-btn-primary {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 100%
  );
  color: #2563eb;
  font-weight: 600;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  letter-spacing: 0.025em;
  font-size: 0.95rem;
}

.modern-cta-btn-primary:hover {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 1) 0%,
    rgba(248, 250, 252, 0.95) 100%
  );
  transform: translateY(-2px);
  box-shadow:
    0 8px 15px -3px rgba(0, 0, 0, 0.15),
    0 4px 6px -2px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  color: #1d4ed8;
}

.modern-cta-btn-secondary {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  color: white;
  font-weight: 600;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  letter-spacing: 0.025em;
  font-size: 0.95rem;
}

.modern-cta-btn-secondary:hover {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 100%
  );
  color: #2563eb;
  border-color: rgba(255, 255, 255, 0.8);
  transform: translateY(-2px);
  box-shadow:
    0 8px 15px -3px rgba(0, 0, 0, 0.15),
    0 4px 6px -2px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Responsive CTA Enhancements */
@media (max-width: 640px) {
  .modern-cta-container {
    padding: 2rem 1.5rem;
    margin-left: 1rem;
    margin-right: 1rem;
  }

  .modern-cta-heading {
    font-size: 2rem;
  }

  .modern-cta-description {
    font-size: 1rem;
  }

  .modern-cta-btn-primary,
  .modern-cta-btn-secondary {
    padding: 0.875rem 1.5rem;
    font-size: 0.875rem;
    width: 100%;
    max-width: 280px;
  }
}

@media (min-width: 641px) and (max-width: 1023px) {
  .modern-cta-container {
    padding: 2.5rem 2rem;
  }

  .modern-cta-btn-primary,
  .modern-cta-btn-secondary {
    padding: 0.875rem 1.75rem;
  }
}

@media (min-width: 1024px) {
  .modern-cta-container {
    padding: 3.5rem 3rem;
  }

  .modern-cta-btn-primary,
  .modern-cta-btn-secondary {
    padding: 1.125rem 2.5rem;
    font-size: 1rem;
  }
}

/* Enhanced focus states for accessibility */
.modern-cta-btn-primary:focus,
.modern-cta-btn-secondary:focus {
  outline: 2px solid rgba(255, 255, 255, 0.8);
  outline-offset: 2px;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .modern-cta-btn-primary,
  .modern-cta-btn-secondary {
    transition: none;
  }

  .modern-cta-btn-primary:hover,
  .modern-cta-btn-secondary:hover {
    transform: none;
  }
}

/* Enhanced visual effects for larger screens */
@media (min-width: 1280px) {
  .modern-cta-container {
    box-shadow:
      0 25px 30px -5px rgba(59, 130, 246, 0.35),
      0 15px 15px -5px rgba(59, 130, 246, 0.25),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
  }
}

/* Line clamp utilities for text truncation */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
