import { useState, useEffect } from 'react';
import { X, ChevronLeft, ChevronRight, Loader2 } from 'lucide-react';

const ProjectGallery = () => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [loadedImages, setLoadedImages] = useState(new Set());
  const [isLoading, setIsLoading] = useState(true);

  const categories = [
    { id: 'all', name: 'All Projects' },
    { id: 'construction', name: 'Construction' },
    { id: 'renovation', name: 'Renovations' },
    { id: 'flooring', name: 'Flooring' },
    { id: 'painting', name: 'Painting' },
    { id: 'maintenance', name: 'Maintenance' }
  ];

  const projects = [
    // Construction Projects
    {
      id: 1,
      title: 'Commercial Building Construction',
      category: 'construction',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.03.00.jpeg',
      description: 'Complete commercial building construction with modern design'
    },
    {
      id: 2,
      title: 'Industrial Facility Development',
      category: 'construction',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.03.01.jpeg',
      description: 'Large-scale industrial facility construction project'
    },
    {
      id: 3,
      title: 'Office Complex Construction',
      category: 'construction',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.03.02.jpeg',
      description: 'Multi-story office complex with premium finishes'
    },
    {
      id: 4,
      title: 'Warehouse Development',
      category: 'construction',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.03.03.jpeg',
      description: 'Modern warehouse facility with specialized requirements'
    },
    {
      id: 5,
      title: 'Retail Center Construction',
      category: 'construction',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.03.10.jpeg',
      description: 'Contemporary retail center with modern amenities'
    },
    {
      id: 6,
      title: 'Corporate Headquarters',
      category: 'construction',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.03.13.jpeg',
      description: 'State-of-the-art corporate headquarters building'
    },
    {
      id: 7,
      title: 'Manufacturing Plant',
      category: 'construction',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.03.14.jpeg',
      description: 'Industrial manufacturing plant construction'
    },
    {
      id: 8,
      title: 'Commercial Plaza',
      category: 'construction',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.03.15.jpeg',
      description: 'Multi-use commercial plaza development'
    },
    {
      id: 9,
      title: 'Business Park Development',
      category: 'construction',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.03.16.jpeg',
      description: 'Modern business park with multiple buildings'
    },
    {
      id: 10,
      title: 'Distribution Center',
      category: 'construction',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.03.17.jpeg',
      description: 'Large-scale distribution center construction'
    },
    {
      id: 11,
      title: 'Technology Hub',
      category: 'construction',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.03.18.jpeg',
      description: 'Modern technology hub with advanced infrastructure'
    },
    {
      id: 12,
      title: 'Service Center Construction',
      category: 'construction',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.03.19.jpeg',
      description: 'Professional service center facility'
    },

    // Renovation Projects
    {
      id: 13,
      title: 'Office Space Renovation',
      category: 'renovation',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.03.20.jpeg',
      description: 'Complete office space transformation with modern design'
    },
    {
      id: 14,
      title: 'Retail Store Makeover',
      category: 'renovation',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.03.21.jpeg',
      description: 'Contemporary retail space renovation'
    },
    {
      id: 15,
      title: 'Restaurant Renovation',
      category: 'renovation',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.03.22.jpeg',
      description: 'Complete restaurant interior renovation'
    },
    {
      id: 16,
      title: 'Medical Facility Upgrade',
      category: 'renovation',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.03.23.jpeg',
      description: 'Healthcare facility renovation and modernization'
    },
    {
      id: 17,
      title: 'Corporate Lobby Renovation',
      category: 'renovation',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.03.53.jpeg',
      description: 'Elegant corporate lobby transformation'
    },
    {
      id: 18,
      title: 'Conference Room Upgrade',
      category: 'renovation',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.03.55.jpeg',
      description: 'Modern conference room renovation'
    },
    {
      id: 19,
      title: 'Showroom Renovation',
      category: 'renovation',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.03.57.jpeg',
      description: 'Premium showroom space renovation'
    },
    {
      id: 20,
      title: 'Training Center Upgrade',
      category: 'renovation',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.03.58.jpeg',
      description: 'Professional training center renovation'
    },
    {
      id: 21,
      title: 'Reception Area Makeover',
      category: 'renovation',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.03.59.jpeg',
      description: 'Welcoming reception area transformation'
    },
    {
      id: 22,
      title: 'Break Room Renovation',
      category: 'renovation',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.04.00.jpeg',
      description: 'Modern employee break room renovation'
    },
    {
      id: 23,
      title: 'Executive Office Upgrade',
      category: 'renovation',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.04.01.jpeg',
      description: 'Luxury executive office renovation'
    },
    {
      id: 24,
      title: 'Workspace Modernization',
      category: 'renovation',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.04.02.jpeg',
      description: 'Open workspace modernization project'
    },

    // Flooring Projects
    {
      id: 25,
      title: 'Commercial Carpet Installation',
      category: 'flooring',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.04.03.jpeg',
      description: 'Premium carpet installation in corporate office'
    },
    {
      id: 26,
      title: 'Luxury Vinyl Flooring',
      category: 'flooring',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.04.04.jpeg',
      description: 'High-end luxury vinyl plank flooring installation'
    },
    {
      id: 27,
      title: 'Hardwood Floor Installation',
      category: 'flooring',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.04.05.jpeg',
      description: 'Premium hardwood flooring for executive offices'
    },
    {
      id: 28,
      title: 'Tile Flooring Project',
      category: 'flooring',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.05.26.jpeg',
      description: 'Professional tile flooring installation'
    },
    {
      id: 29,
      title: 'Laminate Flooring Installation',
      category: 'flooring',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.05.28.jpeg',
      description: 'Durable laminate flooring for high-traffic areas'
    },
    {
      id: 30,
      title: 'Epoxy Floor Coating',
      category: 'flooring',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.05.29.jpeg',
      description: 'Industrial epoxy floor coating application'
    },
    {
      id: 31,
      title: 'Polished Concrete Floors',
      category: 'flooring',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.05.30.jpeg',
      description: 'Modern polished concrete flooring solution'
    },
    {
      id: 32,
      title: 'Rubber Flooring Installation',
      category: 'flooring',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.05.31.jpeg',
      description: 'Specialized rubber flooring for fitness facilities'
    },
    {
      id: 33,
      title: 'Ceramic Tile Installation',
      category: 'flooring',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.05.32.jpeg',
      description: 'Premium ceramic tile flooring installation'
    },
    {
      id: 34,
      title: 'Engineered Wood Flooring',
      category: 'flooring',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.05.33.jpeg',
      description: 'Engineered wood flooring for modern spaces'
    },
    {
      id: 35,
      title: 'Commercial Vinyl Installation',
      category: 'flooring',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.05.35.jpeg',
      description: 'Heavy-duty commercial vinyl flooring'
    },
    {
      id: 36,
      title: 'Stone Flooring Project',
      category: 'flooring',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.05.36.jpeg',
      description: 'Natural stone flooring installation'
    },

    // Painting Projects
    {
      id: 37,
      title: 'Exterior Building Painting',
      category: 'painting',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.05.38.jpeg',
      description: 'Professional exterior building painting with Plascon products'
    },
    {
      id: 38,
      title: 'Interior Office Painting',
      category: 'painting',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.05.40.jpeg',
      description: 'Custom interior painting with premium finishes'
    },
    {
      id: 39,
      title: 'Warehouse Painting Project',
      category: 'painting',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.05.41.jpeg',
      description: 'Industrial warehouse painting and coating'
    },
    {
      id: 40,
      title: 'Retail Space Painting',
      category: 'painting',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.05.42.jpeg',
      description: 'Vibrant retail space painting project'
    },
    {
      id: 41,
      title: 'Corporate Painting Services',
      category: 'painting',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.05.43.jpeg',
      description: 'Professional corporate painting services'
    },
    {
      id: 42,
      title: 'Specialty Coating Application',
      category: 'painting',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.05.44.jpeg',
      description: 'Specialized protective coating application'
    },
    {
      id: 43,
      title: 'Multi-Story Building Painting',
      category: 'painting',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.05.45.jpeg',
      description: 'Large-scale multi-story building painting'
    },
    {
      id: 44,
      title: 'Decorative Wall Finishes',
      category: 'painting',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.05.46.jpeg',
      description: 'Custom decorative wall finishes and textures'
    },
    {
      id: 45,
      title: 'Fire-Resistant Coating',
      category: 'painting',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.07.02.jpeg',
      description: 'Fire-resistant coating for industrial applications'
    },
    {
      id: 46,
      title: 'Waterproof Coating System',
      category: 'painting',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.07.05.jpeg',
      description: 'Advanced waterproof coating system installation'
    },
    {
      id: 47,
      title: 'Anti-Corrosion Painting',
      category: 'painting',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.07.07.jpeg',
      description: 'Anti-corrosion painting for metal structures'
    },
    {
      id: 48,
      title: 'Epoxy Wall Coating',
      category: 'painting',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.07.08.jpeg',
      description: 'Durable epoxy wall coating application'
    },

    // Maintenance Projects
    {
      id: 49,
      title: 'Facility Maintenance Services',
      category: 'maintenance',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.07.09.jpeg',
      description: 'Comprehensive facility maintenance and upkeep'
    },
    {
      id: 50,
      title: 'HVAC System Maintenance',
      category: 'maintenance',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.07.10.jpeg',
      description: 'Professional HVAC system maintenance and repair'
    },
    {
      id: 51,
      title: 'Plumbing Maintenance',
      category: 'maintenance',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.07.11.jpeg',
      description: 'Commercial plumbing maintenance services'
    },
    {
      id: 52,
      title: 'Electrical Maintenance',
      category: 'maintenance',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.07.14.jpeg',
      description: 'Professional electrical system maintenance'
    },
    {
      id: 53,
      title: 'Building Exterior Maintenance',
      category: 'maintenance',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.07.15.jpeg',
      description: 'Exterior building maintenance and repairs'
    },
    {
      id: 54,
      title: 'Roof Maintenance Services',
      category: 'maintenance',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.07.16.jpeg',
      description: 'Professional roof maintenance and inspection'
    },
    {
      id: 55,
      title: 'Preventive Maintenance',
      category: 'maintenance',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.07.17.jpeg',
      description: 'Scheduled preventive maintenance programs'
    },
    {
      id: 56,
      title: 'Emergency Repair Services',
      category: 'maintenance',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.07.19.jpeg',
      description: '24/7 emergency repair and maintenance services'
    },
    {
      id: 57,
      title: 'Equipment Maintenance',
      category: 'maintenance',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.07.20.jpeg',
      description: 'Industrial equipment maintenance and servicing'
    },
    {
      id: 58,
      title: 'Safety System Maintenance',
      category: 'maintenance',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.07.21.jpeg',
      description: 'Fire safety and security system maintenance'
    },
    {
      id: 59,
      title: 'Grounds Maintenance',
      category: 'maintenance',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.07.23.jpeg',
      description: 'Commercial grounds and landscaping maintenance'
    },
    {
      id: 60,
      title: 'Cleaning and Janitorial Services',
      category: 'maintenance',
      image: '/images/areboneGallery/WhatsApp Image 2025-07-08 at 22.07.25.jpeg',
      description: 'Professional cleaning and janitorial maintenance'
    }];

  const filteredProjects = selectedCategory === 'all'
    ? projects
    : projects.filter(project => project.category === selectedCategory);

  // Handle image loading
  const handleImageLoad = (imageId) => {
    setLoadedImages(prev => new Set([...prev, imageId]));
  };

  // Initialize loading state
  useEffect(() => {
    setIsLoading(true);
    const timer = setTimeout(() => setIsLoading(false), 1000);
    return () => clearTimeout(timer);
  }, [selectedCategory]);

  const openLightbox = (project) => {
    setSelectedImage(project);
  };

  const closeLightbox = () => {
    setSelectedImage(null);
  };

  const navigateImage = (direction) => {
    const currentIndex = filteredProjects.findIndex(p => p.id === selectedImage.id);
    let newIndex;

    if (direction === 'next') {
      newIndex = (currentIndex + 1) % filteredProjects.length;
    } else {
      newIndex = currentIndex === 0 ? filteredProjects.length - 1 : currentIndex - 1;
    }

    setSelectedImage(filteredProjects[newIndex]);
  };

  return (
    <section className="section-padding bg-white">
      <div className="container-custom">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="heading-lg text-secondary-900 mb-4">
            Our Project Gallery
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Explore our portfolio of completed projects showcasing quality workmanship
            and professional excellence across various construction services.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-6 py-3 rounded-lg font-medium transition-colors duration-200 ${selectedCategory === category.id
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
            >
              {category.name}
            </button>
          ))}
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary-600" />
            <span className="ml-2 text-gray-600">Loading gallery...</span>
          </div>
        )}

        {/* Projects Grid */}
        {!isLoading && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredProjects.map((project) => (
              <div
                key={project.id}
                className="group cursor-pointer transform transition-all duration-300 hover:-translate-y-2"
                onClick={() => openLightbox(project)}
              >
                <div className="relative overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-shadow duration-300">
                  {/* Image Loading Placeholder */}
                  {!loadedImages.has(project.id) && (
                    <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
                      <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
                    </div>
                  )}

                  <img
                    src={project.image}
                    alt={project.title}
                    className={`w-full h-64 object-cover group-hover:scale-105 transition-all duration-300 ${loadedImages.has(project.id) ? 'opacity-100' : 'opacity-0'
                      }`}
                    onLoad={() => handleImageLoad(project.id)}
                    loading="lazy"
                  />

                  {/* Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-end">
                    <div className="text-white p-4 w-full">
                      <h3 className="text-lg font-semibold mb-1 line-clamp-2">{project.title}</h3>
                      <p className="text-sm text-gray-200 line-clamp-2">{project.description}</p>
                    </div>
                  </div>

                  {/* Category Badge */}
                  <div className="absolute top-3 left-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <span className="bg-primary-600 text-white text-xs font-medium px-2 py-1 rounded-full capitalize">
                      {project.category}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* No Results Message */}
        {!isLoading && filteredProjects.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No projects found in this category.</p>
          </div>
        )}

        {/* Lightbox */}
        {selectedImage && (
          <div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4">
            <div className="relative max-w-4xl max-h-full">
              {/* Close Button */}
              <button
                onClick={closeLightbox}
                className="absolute top-4 right-4 text-white hover:text-gray-300 z-10"
              >
                <X className="h-8 w-8" />
              </button>

              {/* Navigation Buttons */}
              <button
                onClick={() => navigateImage('prev')}
                className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 z-10"
              >
                <ChevronLeft className="h-8 w-8" />
              </button>
              <button
                onClick={() => navigateImage('next')}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 z-10"
              >
                <ChevronRight className="h-8 w-8" />
              </button>

              {/* Image */}
              <img
                src={selectedImage.image}
                alt={selectedImage.title}
                className="max-w-full max-h-full object-contain"
              />

              {/* Image Info */}
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6 text-white">
                <h3 className="text-xl font-semibold mb-2">{selectedImage.title}</h3>
                <p className="text-gray-300">{selectedImage.description}</p>
              </div>
            </div>
          </div>
        )}

        {/* CTA Section */}
        <div className="text-center mt-16">
          <div className="bg-gray-50 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-secondary-900 mb-4">
              Ready to Start Your Project?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Let us bring your vision to life with the same quality and attention to detail
              showcased in our portfolio. Contact us today for a free consultation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-primary">
                Request Free Quote
              </button>
              <button className="btn-secondary">
                Schedule Consultation
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProjectGallery;
