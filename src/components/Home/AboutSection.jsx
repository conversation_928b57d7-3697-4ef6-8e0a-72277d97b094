import { Target, Users, Award, TrendingUp } from 'lucide-react';

const AboutSection = () => {
  const values = [
    {
      icon: Target,
      title: 'Excellence & Quality',
      description: 'Committed to delivering the highest standards in every project we undertake.'
    },
    {
      icon: Users,
      title: 'Youth Development',
      description: 'Empowering young people through skills training and career development opportunities.'
    },
    {
      icon: Award,
      title: 'Professional Standards',
      description: 'Maintaining high work ethics and industry standards as certified professionals.'
    },
    {
      icon: TrendingUp,
      title: 'Growth & Innovation',
      description: 'Continuously evolving our services and embracing new construction technologies.'
    }
  ];

  return (
    <section className="section-padding modern-about-section">
      <div className="container-custom modern-content-container">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Content */}
          <div className="space-y-8">
            <div className="space-y-6">
              <h2 className="text-4xl lg:text-5xl modern-heading-primary">
                Building a Better Future Through
                <span className="bg-gradient-to-r from-orange-600 to-orange-700 bg-clip-text text-transparent"> Excellence</span>
              </h2>
              <p className="modern-text-enhanced text-lg">
                Arebone Building Enterprise (ABE) was established in 2012 by Dwaine Moth as a vehicle
                for technical advancement and youth development in South Africa's construction industry.
              </p>
            </div>

            <div className="space-y-6">
              <p className="modern-text-enhanced">
                Our mission extends beyond construction – we're committed to addressing the skills shortage
                in South Africa by training and developing young people for successful careers in construction.
                We believe in not just giving someone a 'fish' for a day, but teaching them to 'fish' for a lifetime.
              </p>

              <p className="modern-text-enhanced">
                As a preferred Belgotex flooring installer working with various quality suppliers, we maintain
                the highest industry standards while serving major clients across seven cities in South Africa.
              </p>
            </div>

            {/* Mission Statement */}
            <div className="modern-mission-card p-8">
              <h3 className="font-bold text-slate-900 mb-3 text-lg">Our Mission</h3>
              <p className="modern-text-enhanced italic text-base">
                "To develop and preserve a good name in the industry and help maintain high work
                and ethical standards for our customers while empowering the next generation."
              </p>
            </div>

            {/* CTA */}
            <div className="modern-cta-container">
              <button className="modern-btn-primary">
                Learn More About Us
              </button>
              <button className="modern-btn-secondary">
                View Our Projects
              </button>
            </div>
          </div>

          {/* Values Grid */}
          <div className="modern-values-container space-y-8">
            <div className="text-center lg:text-left">
              <h3 className="text-3xl lg:text-4xl modern-values-heading mb-6">
                Our Core Values
              </h3>
              <p className="modern-values-description text-lg">
                The principles that guide everything we do and every project we deliver.
              </p>
            </div>

            <div className="modern-values-grid">
              {values.map((value, index) => {
                const IconComponent = value.icon;
                return (
                  <div
                    key={index}
                    className="modern-value-card"
                  >
                    <div className="modern-value-icon">
                      <IconComponent className="h-6 w-6 text-orange-600" />
                    </div>
                    <h4 className="modern-value-title">
                      {value.title}
                    </h4>
                    <p className="modern-value-description">
                      {value.description}
                    </p>
                  </div>
                );
              })}
            </div>

            {/* Achievement Highlight */}
            <div className="modern-achievement-card">
              <div className="flex items-center space-x-4">
                <div className="modern-achievement-icon">
                  <Award className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h4 className="modern-achievement-title">Property Point Partnership</h4>
                  <p className="modern-achievement-description">
                    Selected for a two-year incubation program with Property Point,
                    a Growthpoint initiative.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
